/**
 * 聚焦子节点标签功能测试
 * 
 * 测试场景：
 * 1. 点击节点进入聚焦状态时，应该显示所有子节点的标签
 * 2. 切换到新的聚焦节点时，应该清理旧的子节点标签并显示新的
 * 3. 退出聚焦模式时，应该清理所有子节点标签
 * 4. 回退操作时，应该正确管理子节点标签的显示和隐藏
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import focusChildrenLabelManager from '../src/components/ksgMap/core/FocusChildrenLabelManager';
import { KsgLabel } from '../src/components/ksgMap/core/KsgLabel';
import type { Point } from '../src/components/ksgMap/types';

// 模拟测试数据
const mockPoints: Point[] = [
  {
    id: 'parent-1',
    name: '父节点1',
    parentIds: [],
    childIds: ['child-1', 'child-2', 'child-3'],
    level: 0,
    coordinate: [0, 0, 0],
    isMilestone: false,
    status: 1,
    index: 0
  },
  {
    id: 'child-1',
    name: '子节点1',
    parentIds: ['parent-1'],
    childIds: [],
    level: 1,
    coordinate: [-2, -2, 0],
    isMilestone: false,
    status: 0,
    index: 1
  },
  {
    id: 'child-2',
    name: '子节点2',
    parentIds: ['parent-1'],
    childIds: [],
    level: 1,
    coordinate: [0, -2, 0],
    isMilestone: false,
    status: 0,
    index: 2
  },
  {
    id: 'child-3',
    name: '子节点3',
    parentIds: ['parent-1'],
    childIds: [],
    level: 1,
    coordinate: [2, -2, 0],
    isMilestone: false,
    status: 0,
    index: 3
  }
];

// 模拟ctx对象
const mockCtx = {
  viewGroup: {
    add: vi.fn(),
    remove: vi.fn()
  }
};

// 模拟全局ctx
vi.mock('../src/components/ksgMap/ctx', () => ({
  default: mockCtx
}));

describe('FocusChildrenLabelManager', () => {
  beforeEach(() => {
    // 重置管理器状态
    focusChildrenLabelManager.clearChildrenLabels();
    vi.clearAllMocks();
  });

  afterEach(() => {
    // 清理测试后的状态
    focusChildrenLabelManager.clearChildrenLabels();
  });

  describe('showChildrenLabels', () => {
    it('应该为所有子节点创建并显示标签', () => {
      const childrenPoints = mockPoints.slice(1); // 获取子节点
      
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      
      // 验证标签数量
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 验证每个子节点都有对应的标签
      childrenPoints.forEach(point => {
        expect(focusChildrenLabelManager.hasActiveLabel(point.id)).toBe(true);
      });
      
      // 验证添加到场景的次数
      expect(mockCtx.viewGroup.add).toHaveBeenCalledTimes(3);
    });

    it('应该避免重复创建同一节点的标签', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 第一次显示
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 第二次显示相同的节点
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 验证添加到场景的次数没有增加
      expect(mockCtx.viewGroup.add).toHaveBeenCalledTimes(3);
    });

    it('当viewGroup不存在时应该安全处理', () => {
      const originalViewGroup = mockCtx.viewGroup;
      mockCtx.viewGroup = null;
      
      const childrenPoints = mockPoints.slice(1);
      
      expect(() => {
        focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      }).not.toThrow();
      
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
      
      // 恢复原始状态
      mockCtx.viewGroup = originalViewGroup;
    });
  });

  describe('hideChildrenLabels', () => {
    it('应该隐藏所有活跃的子节点标签', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 先显示标签
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 隐藏标签
      focusChildrenLabelManager.hideChildrenLabels();
      
      // 标签仍然存在但应该被隐藏
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 验证每个标签的hide方法被调用
      childrenPoints.forEach(point => {
        const label = focusChildrenLabelManager.getLabel(point.id);
        expect(label).toBeDefined();
      });
    });
  });

  describe('clearChildrenLabels', () => {
    it('应该完全清理所有子节点标签', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 先显示标签
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 清理标签
      focusChildrenLabelManager.clearChildrenLabels();
      
      // 验证标签被完全清理
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
      
      // 验证从场景中移除的次数
      expect(mockCtx.viewGroup.remove).toHaveBeenCalledTimes(3);
      
      // 验证标签不再存在
      childrenPoints.forEach(point => {
        expect(focusChildrenLabelManager.hasActiveLabel(point.id)).toBe(false);
      });
    });

    it('当没有活跃标签时应该安全处理', () => {
      expect(() => {
        focusChildrenLabelManager.clearChildrenLabels();
      }).not.toThrow();
      
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
    });
  });

  describe('updateChildLabelPosition', () => {
    it('应该更新指定子节点标签的位置', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 先显示标签
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      
      const targetPoint = childrenPoints[0];
      const newPosition: [number, number, number] = [5, 5, 5];
      
      // 更新位置
      focusChildrenLabelManager.updateChildLabelPosition(targetPoint.id, newPosition);
      
      // 验证标签位置被更新（这里需要mock KsgLabel的updatePosition方法）
      const label = focusChildrenLabelManager.getLabel(targetPoint.id);
      expect(label).toBeDefined();
    });

    it('当标签不存在时应该安全处理', () => {
      const newPosition: [number, number, number] = [5, 5, 5];
      
      expect(() => {
        focusChildrenLabelManager.updateChildLabelPosition('non-existent', newPosition);
      }).not.toThrow();
    });
  });

  describe('setEnabled', () => {
    it('禁用时应该清理所有标签', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 先显示标签
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 禁用功能
      focusChildrenLabelManager.setEnabled(false);
      
      // 验证标签被清理
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
    });

    it('禁用状态下不应该显示新标签', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 先禁用功能
      focusChildrenLabelManager.setEnabled(false);
      
      // 尝试显示标签
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      
      // 验证标签没有被创建
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
    });
  });

  describe('dispose', () => {
    it('应该清理所有资源并禁用功能', () => {
      const childrenPoints = mockPoints.slice(1);
      
      // 先显示标签
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(3);
      
      // 销毁管理器
      focusChildrenLabelManager.dispose();
      
      // 验证资源被清理
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
      
      // 验证功能被禁用
      focusChildrenLabelManager.showChildrenLabels(childrenPoints);
      expect(focusChildrenLabelManager.getActiveLabelCount()).toBe(0);
    });
  });
});
