import { Vector3 } from "three";
import { throttle } from "lodash";
import ctx from "../ctx";
import { getDomElement, findValidateParentNode } from "../utils";
import innerViewValidate from "../utils/viewValidate";
import createClickPointEvent from "../utils/clickPointEvent";
import { handleEnterFocus } from "../core/enterFocus";
import { ENTER_FOCUS_MODE, VIEW_MODE, LOAD_STATUS } from "../enums";
import enterGlobalView from "../core/enterGlobalView";
import { createMouseMoveEvent } from "../utils/globalViewEvent";
import createSceneHover from "../utils/hoverObjectEvent";
import type { EventsCallback, Point, Size } from "../types";
import ksgHover from "../core/KsgHover";
import focusCrust from "../core/focusCrust";
import { globalLabelManager } from "../utils/globalViewLabelManager";
import { hoverLabel, focusLabel } from "../core/KsgLabel";
import focusChildrenLabelManager from "../core/FocusChildrenLabelManager";
import TWEEN from "@tweenjs/tween.js";

/**
 * 事件系统初始化函数 - 知识图谱交互事件的核心管理器
 *
 * 这个函数是整个知识图谱交互系统的核心，负责管理所有的用户交互事件
 * 包括鼠标悬停、点击、双击、拖拽等各种交互行为的处理
 *
 * 主要功能：
 * - 节点悬停效果和标签显示
 * - 节点点击进入聚焦模式
 * - 双击进入全局视角
 * - 相机控制器事件管理
 * - 数据懒加载触发
 * - 视角切换和历史记录管理
 *
 * @param wrapperElSize 容器元素的尺寸信息
 * @param events 外部事件回调函数集合
 * @returns 返回事件管理相关的函数集合
 */
export default function useInitEvents(
  wrapperElSize: Size,
  events: EventsCallback
) {
  // 容器DOM元素引用 - 用于事件绑定和解绑
  let wrapperEle: HTMLElement | null;

  // 将交互相关的3D对象添加到场景中
  // 这些对象负责提供视觉反馈和用户交互效果
  ctx.viewGroup?.add(ksgHover);    // 悬停时的光圈效果
  ctx.viewGroup?.add(focusCrust);  // 聚焦节点的外壳效果
  ctx.viewGroup?.add(hoverLabel);  // 悬停时显示的标签
  ctx.viewGroup?.add(focusLabel);  // 聚焦节点的标签

  // 悬停功能开关 - 控制是否响应鼠标悬停事件
  // 在某些操作过程中需要临时禁用悬停以避免干扰
  let enableHover = true;

  // 创建场景悬停事件处理器
  // 这个处理器负责检测鼠标在3D场景中悬停在哪个节点上
  let {
    clear,                          // 清理事件监听器的函数
    updateSize: updateHoverEventSize, // 更新事件处理器尺寸的函数
    event,                          // 鼠标移动事件处理函数
  } = createSceneHover(
    wrapperElSize, // 容器尺寸
    ctx,           // 全局上下文

    // 鼠标悬停到节点时的回调函数
    (point: Point) => {
      // 获取节点在屏幕上的2D坐标（设备无关坐标）
      const pointDnc = ctx.pointsMesh?.getWorldP(
        point.index!,
        ctx.camera!,
        wrapperElSize.width * window.devicePixelRatio,   // 考虑设备像素比的宽度
        wrapperElSize.height * window.devicePixelRatio   // 考虑设备像素比的高度
      )!;

      // 检查是否应该响应悬停事件
      if (
        ctx.pointsMesh?.focusIndex === point.index ||        // 当前节点已经是聚焦节点
        ctx.pointsMesh?.lastHoverIndex === point.index ||    // 当前节点已经是悬停节点
        !enableHover ||                                      // 悬停功能被禁用
        !innerViewValidate(ctx.viewRange!, { ...pointDnc }) // 节点不在可视范围内
      )
        return;

      // 切换节点的悬停状态（高亮显示）
      ctx.pointsMesh?.toggleHover(point.index!);

      // 显示悬停光圈效果
      ksgHover.display(point);

      // 显示悬停标签
      hoverLabel.display(point, {
        viewRange: ctx.viewRange!,  // 视口范围
        dnc: pointDnc,             // 节点的屏幕坐标
      });

      // 改变鼠标样式为指针，提示用户可以点击
      document.body.style.cursor = "pointer";

      // 如果当前在全局视角模式，停止自动旋转
      if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
        ctx.controls!.autoRotate = false;
        clearMoveEvent(); // 清除鼠标移动事件监听
      }
    },

    // 鼠标离开节点时的回调函数
    () => {
      ksgHover.hide();                        // 隐藏悬停光圈
      hoverLabel.hide();                      // 隐藏悬停标签
      ctx.pointsMesh?.toggleHover();          // 取消节点高亮
      document.body.style.cursor = "default"; // 恢复默认鼠标样式
    }
  );

  // 创建节点点击事件进入子图
  let {
    clear: clearFocusEvent,
    updateSize: updateClickEventSize,
    event: handleFocusEvent,
  } = createClickPointEvent(wrapperElSize, ctx, (point: Point) => {
    if (
      (!ctx.controls?.enabled && point) ||
      ctx.pointsMesh?.focusIndex === point.index ||
      !innerViewValidate(
        ctx.viewRange!,
        ctx.pointsMesh?.getWorldP(
          point.index!,
          ctx.camera!,
          wrapperElSize.width,
          wrapperElSize.height
        )!
      )
    )
      return;
    // 切换模式
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
      ctx.controls!.autoRotate = false;
      ctx.viewMode = VIEW_MODE.Focus_VIEW;
      ctx.pointsMesh!.breathAnimationSwitch(false);
      // 禁用全局标签管理器
      globalLabelManager.disable();
    }
    enableHover = false;
    ksgHover.hide();
    hoverLabel.hide();
    focusLabel.hide();
    // 清理旧的子节点标签
    focusChildrenLabelManager.clearChildrenLabels();
    TWEEN.removeAll(); //清空上一次未执行完的动画
    focusCrust.display(point);
    ctx.pointsMesh?.toggleFocus(point.index!);
    handleEnterFocus(point).then(() => {
      enableHover = true;
    });
  });

  //加载更多知识节点数据事件
  function handleLoadMore(e: any) {
    const { y } = e.target.target;
    // return;
    if (
      y - 1.9 < -ctx.maxLevelY! &&
      ctx.loadStatus === LOAD_STATUS.loaded &&
      ctx.graph!.pointsData.size < ctx.pointsLevelPager!.total!
    ) {
      events?.loadMoreCallback?.(
        ctx.focusPointInfo?.pointId!,
        ctx.pointsLevelPager!.current,
        ctx.pointsLevelPager?.levelSize!
      );
    }
  }

  /**
   * 外部异步结果来修改该加载状态
   */
  function changeLoadStatus(status: LOAD_STATUS) {
    if (
      status === LOAD_STATUS.loaded &&
      ctx.graph?.pointsData.size! < ctx.pointsLevelPager?.total!
    ) {
      ctx.pointsLevelPager!.current += ctx.pointsLevelPager?.levelSize!;
    }

    ctx.loadStatus = status;
  }

  // 监听根节点到相机的距离
  function handleRootToCameraDistance() {
    ctx.isControls = false;
    if (!ctx.pointsMesh || !focusLabel || !focusLabel.visible) return;
    const target = new Vector3(
      ...ctx.pointsMesh!.getPointData(ctx.pointsMesh!.focusIndex)!.coordinate
    );
    target.y += 6;
    const distance = target?.distanceTo(ctx.camera?.position!);
    // 控制label显示
    focusLabel.distanceShow(distance < (ctx.maxDistance ?? 100));
  }

  // 处理标签点击
  function handleLabelClick(e: MouseEvent) {
    if (e.button !== 0) return;
    const dom = getDomElement(e);
    if (dom.nodeName === "DIV") return;
    const id = findValidateParentNode(dom);
    if (id) events?.clickLabelCallback?.(id);
  }

  //处理双击点击进入全局视角
  function handleEnterGlobalView(_: MouseEvent) {
    if (ctx.focusStack![ctx.focusStack!.length - 1] === "null") return;
    const { y: cameraY } = ctx.camera?.position!;
    const peerLevelsY = Object.keys(ctx.graph!.idLevelMap).map(
      (level) =>
        ctx.graph?.getPointById(ctx.graph!.idLevelMap[Number(level)][0]!)
          ?.coordinate[1]
    );

    //计算视角移动的最终位置
    const rang = ctx.levelSpace! / 2;
    let resultY =
      peerLevelsY.length > 1
        ? peerLevelsY?.find(
            (item) => cameraY > item! - rang && cameraY < item! + rang
          )
        : peerLevelsY[0];

    // 多层节点
    if (peerLevelsY.length > 1) {
      //超出最顶部
      if (!resultY && cameraY > peerLevelsY![0]!) {
        resultY = peerLevelsY![1]!;
        //  超出最底部
      } else if (
        !resultY &&
        cameraY < peerLevelsY![peerLevelsY?.length! - 1]!
      ) {
        resultY = peerLevelsY![peerLevelsY?.length! - 2]!;
      }
    }

    // 方向向量
    const offset = ctx.camera?.position
      .clone()
      .sub(new Vector3(0, resultY!, 0));
    //缩放倍率
    offset?.multiplyScalar(2);

    focusLabel.hide();
    enterGlobalView([offset!.x, resultY!, offset!.z])?.then(() => {
      ctx.controls!.autoRotate = true;
      ctx.controls!.autoRotateSpeed = 0.05;// 全局视角旋转速度
      ctx.controls!.enabled = true;
      enableHover = true;
      wrapperEle!.addEventListener("mousemove", handleMoveEvent);
    });
  }
  const throttleHandleEnterGlobalView = throttle(handleEnterGlobalView, 500);
  /**
   *处理全局视角下相机控制器自动旋转相关事件
   */
  function onMove() {
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && ctx.controls!.autoRotate) {
      ctx.controls!.autoRotate = false;
    }
  }
  function onMoveEnd() {
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW && !ctx.controls!.autoRotate) {
      ctx.controls!.autoRotate = true;
    }
  }
  const { handleMoveEvent, clear: clearMoveEvent } = createMouseMoveEvent(
    onMove,
    onMoveEnd,
    1000 //1秒等待时间
  );

  const controlsStart = () => (ctx.isControls = true);

  /**
   * 初始化事件
   */
  function initEvents(containerEle: HTMLElement) {
    wrapperEle = containerEle;
    containerEle.addEventListener("mousemove", event);
    containerEle.addEventListener("pointerdown", handleFocusEvent);
    containerEle.addEventListener("dblclick", throttleHandleEnterGlobalView);
    ctx.controls?.addEventListener("start", controlsStart);
    ctx.controls?.addEventListener("end", handleLoadMore);
    ctx.controls?.addEventListener("end", handleRootToCameraDistance);
    ctx.css2dRenderer?.domElement.addEventListener(
      "pointerdown",
      handleLabelClick
    );
  }
  /**
   * 销毁事件
   */
  function destroyEvents() {
    clear();
    clearFocusEvent();
    clearMoveEvent();
    wrapperEle!.removeEventListener("click", handleFocusEvent);
    wrapperEle!.removeEventListener("dblclick", throttleHandleEnterGlobalView);
    ctx.controls?.removeEventListener("start", controlsStart);
    ctx.controls?.removeEventListener("end", handleLoadMore);
    ctx.controls?.removeEventListener("end", handleRootToCameraDistance);
    ctx.css2dRenderer?.domElement.removeEventListener(
      "click",
      handleLabelClick
    );
  }

  /**
   *回退到根节点
   */
  function focusBackToRoot() {
    if (ctx.focusStack!.length === 1) return;

    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
      wrapperEle!.removeEventListener("mousemove", handleMoveEvent);
      ctx.pointsMesh?.breathAnimationSwitch(false);
      // 禁用全局标签管理器
      globalLabelManager.disable();
    }
    const rootPoint = ctx.pointsMesh!.getPointDataById(ctx.focusStack![0])!;
    TWEEN.removeAll();
    // 清理旧的子节点标签
    focusChildrenLabelManager.clearChildrenLabels();
    focusLabel.display(rootPoint);
    handleEnterFocus(rootPoint);
  }

  /**
   * 聚焦节点历史回退
   */
  function focusBack() {
    if (ctx.focusStack!.length < 2) return;
    ctx.focusStack?.pop();
    // 把进入全局的历史记录忽略
    if (ctx.focusStack![ctx.focusStack!.length - 1] === "null")
      ctx.focusStack?.pop();
    const point = ctx.pointsMesh!.getPointDataById(
      ctx.focusStack![ctx.focusStack!.length - 1]
    );
    if (ctx.viewMode === VIEW_MODE.GLOBAL_VIEW) {
      wrapperEle!.removeEventListener("mousemove", handleMoveEvent);
      ctx.pointsMesh?.breathAnimationSwitch(false);
      // 禁用全局标签管理器
      globalLabelManager.disable();
    }
    TWEEN.removeAll();
    // 清理旧的子节点标签
    focusChildrenLabelManager.clearChildrenLabels();
    focusLabel.display(point!);
    handleEnterFocus(point!, ENTER_FOCUS_MODE.BACK);
  }

  return {
    initEvents,
    destroyEvents,
    focusBackToRoot,
    focusBack,
    updateClickEventSize,
    updateHoverEventSize,
    changeLoadStatus,
  };
}
